'use client'

import { useMemo } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from 'antd-mobile'

import { CartItemCount, JumpToCustomerService } from '@/businessComponents'
import { Service } from '@/components/icons'

import { useProduct } from '../../context/ProductContext'
// 定义产品类型
interface Product {
  isEverythingOutOfStock?: boolean
  isOutOfStock?: boolean
  sku?: string
  images?: Array<{ url: string }>
  paymentNcoin?: boolean
  price?: {
    regular_price?: { value: string | number }
    final_price?: { value: string | number }
  }
  name?: string
}

// 定义 useProduct hook 返回类型
interface UseProductReturn {
  onAddToCartBefore: (type: number) => void
  isBuyNow: boolean
  isLoadingPay: boolean
  productDetails: Product
  productStatus: Product
  isLoggedIn: boolean
}

const ProductBottomBar = () => {
  const {
    onAddToCartBefore,
    isBuyNow,
    isLoadingPay,
    productDetails,
    productStatus: product,
    isLoggedIn,
  } = useProduct() as UseProductReturn

  const getI18nString = useTranslations('Common')

  const productName = productDetails?.name

  const getStatusStock = () => {
    return !(product.isEverythingOutOfStock || product.isOutOfStock)
  }

  const udeskParams = useMemo(() => {
    return {
      type: 'product',
      targetId: product?.sku,
      product: {
        title: productName,
        url: '',
        image: product?.images?.[0]?.url,
        价格: `${product?.paymentNcoin ? 'N' : '¥'} ${product?.price?.regular_price?.value}`,
        促销价: `${product?.paymentNcoin ? 'N' : '¥'} ${product?.price?.final_price?.value}`,
      },
    }
  }, [product, productName])

  return (
    <div className="max-container fixed bottom-0 z-10 w-full max-w-mobile-base bg-white p-base-16">
      <div className="flex items-center gap-base-16">
        <div className="flex items-center gap-base-16">
          <JumpToCustomerService
            containerStyle={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            udeskParams={udeskParams}>
            <Service />
            <span className="inline-block text-sm">{getI18nString('product_cs')}</span>
          </JumpToCustomerService>
          <CartItemCount isPdp sku={product?.sku} isLoggedIn={isLoggedIn}>
            <span className="inline-block text-sm">{getI18nString('cart')}</span>
          </CartItemCount>
        </div>

        <div className="flex flex-1 gap-base font-miSansDemiBold450">
          {getStatusStock() ? (
            isBuyNow ? (
              <Button
                className="nb-button w-full !py-[13px]"
                onClick={() => onAddToCartBefore(2)}
                color="primary"
                disabled={isLoadingPay}>
                {getI18nString('product_buy_now')}
              </Button>
            ) : (
              <>
                <Button
                  className="nb-button w-full !py-[13px]"
                  onClick={() => onAddToCartBefore(1)}
                  disabled={isLoadingPay}>
                  {getI18nString('product_add_cart')}
                </Button>
                <Button
                  className="nb-button w-full !py-[13px]"
                  onClick={() => onAddToCartBefore(2)}
                  color="primary"
                  disabled={isLoadingPay}>
                  {getI18nString('product_buy_now')}
                </Button>
              </>
            )
          ) : (
            <Button disabled className="nb-button w-full !py-[13px]">
              {getI18nString('product_sale_out')}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export default ProductBottomBar
