/**
 * 高德地图使用示例
 * 展示如何在React组件中使用更新后的高德地图API
 */

'use client'

import React, { useEffect, useState } from 'react'
import { useLocation } from '@ninebot/core'

interface LocationInfo {
  latitude: number | null
  longitude: number | null
  address?: string
  accuracy?: number
  error?: string
}

const AMapUsageExample: React.FC = () => {
  const [locationInfo, setLocationInfo] = useState<LocationInfo>({
    latitude: null,
    longitude: null
  })
  const [isLoading, setIsLoading] = useState(false)
  const [mapContainer, setMapContainer] = useState<HTMLDivElement | null>(null)
  
  const { getLocation, reverseGeocode, getMapInstance } = useLocation()

  // 获取用户位置
  const handleGetLocation = async () => {
    setIsLoading(true)
    try {
      const location = await getLocation()
      setLocationInfo(location)
      
      if (location.latitude && location.longitude) {
        console.log('获取位置成功:', location)
      } else {
        console.error('获取位置失败:', location.error)
      }
    } catch (error) {
      console.error('获取位置异常:', error)
      setLocationInfo({
        latitude: null,
        longitude: null,
        error: error instanceof Error ? error.message : '获取位置失败'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 逆地理编码
  const handleReverseGeocode = async () => {
    if (!locationInfo.latitude || !locationInfo.longitude) {
      alert('请先获取位置信息')
      return
    }

    setIsLoading(true)
    try {
      const result = await reverseGeocode(locationInfo.latitude, locationInfo.longitude)
      console.log('逆地理编码结果:', result)
      
      if (result.regeocode?.formatted_address) {
        setLocationInfo(prev => ({
          ...prev,
          address: result.regeocode.formatted_address
        }))
      }
    } catch (error) {
      console.error('逆地理编码失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 创建地图实例
  const handleCreateMap = async () => {
    if (!mapContainer) {
      alert('地图容器未准备好')
      return
    }

    if (!locationInfo.latitude || !locationInfo.longitude) {
      alert('请先获取位置信息')
      return
    }

    setIsLoading(true)
    try {
      const mapInstance = await getMapInstance('map-container', {
        zoom: 15,
        center: [locationInfo.longitude, locationInfo.latitude],
        mapStyle: 'amap://styles/normal'
      })

      if (mapInstance) {
        console.log('地图创建成功:', mapInstance)
        
        // 添加标记
        const marker = new window.AMap.Marker({
          position: [locationInfo.longitude, locationInfo.latitude],
          title: '当前位置'
        })
        
        mapInstance.add(marker)
      }
    } catch (error) {
      console.error('创建地图失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>高德地图安全代理使用示例</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>功能测试</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={handleGetLocation} 
            disabled={isLoading}
            style={{
              padding: '10px 20px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isLoading ? 'not-allowed' : 'pointer'
            }}
          >
            {isLoading ? '获取中...' : '获取位置'}
          </button>
          
          <button 
            onClick={handleReverseGeocode} 
            disabled={isLoading || !locationInfo.latitude}
            style={{
              padding: '10px 20px',
              backgroundColor: '#52c41a',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: (isLoading || !locationInfo.latitude) ? 'not-allowed' : 'pointer'
            }}
          >
            {isLoading ? '编码中...' : '逆地理编码'}
          </button>
          
          <button 
            onClick={handleCreateMap} 
            disabled={isLoading || !locationInfo.latitude}
            style={{
              padding: '10px 20px',
              backgroundColor: '#722ed1',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: (isLoading || !locationInfo.latitude) ? 'not-allowed' : 'pointer'
            }}
          >
            {isLoading ? '创建中...' : '创建地图'}
          </button>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>位置信息</h2>
        <div style={{
          padding: '15px',
          backgroundColor: '#f5f5f5',
          borderRadius: '4px',
          fontFamily: 'monospace'
        }}>
          {locationInfo.error ? (
            <div style={{ color: '#ff4d4f' }}>
              错误: {locationInfo.error}
            </div>
          ) : (
            <>
              <div>纬度: {locationInfo.latitude || '未获取'}</div>
              <div>经度: {locationInfo.longitude || '未获取'}</div>
              <div>地址: {locationInfo.address || '未获取'}</div>
              <div>精度: {locationInfo.accuracy ? `${locationInfo.accuracy}米` : '未获取'}</div>
            </>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>地图容器</h2>
        <div 
          id="map-container"
          ref={setMapContainer}
          style={{
            width: '100%',
            height: '400px',
            backgroundColor: '#f0f0f0',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#999'
          }}
        >
          {mapContainer ? '地图将在这里显示' : '地图容器准备中...'}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>使用说明</h2>
        <ol style={{ lineHeight: '1.6' }}>
          <li>点击"获取位置"按钮获取当前位置信息</li>
          <li>获取位置成功后，可以点击"逆地理编码"获取详细地址</li>
          <li>点击"创建地图"在下方容器中显示地图</li>
          <li>所有操作都通过安全的API代理进行，密钥不会暴露给客户端</li>
        </ol>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>安全特性</h2>
        <ul style={{ lineHeight: '1.6' }}>
          <li>✅ API密钥和安全密钥存储在服务器端</li>
          <li>✅ 客户端通过API代理访问高德地图服务</li>
          <li>✅ 支持请求频率限制</li>
          <li>✅ 支持Redis缓存优化性能</li>
          <li>✅ 支持CORS和来源验证</li>
          <li>✅ 详细的错误处理和日志记录</li>
        </ul>
      </div>
    </div>
  )
}

export default AMapUsageExample
