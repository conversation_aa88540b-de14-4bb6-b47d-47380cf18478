import { useCallback } from 'react'
import { useDispatch } from 'react-redux'

import { useLazyGetCheckoutCartQuery } from '../services'
import { setAllCartProducts, TCheckoutCartProductItems } from '../store'
import { formatProducts } from '../utils'

/**
 * 获取购物车信息 Hook
 */
const useCheckoutCart = () => {
  const dispatch = useDispatch()

  const [getCheckoutCartQuery, { isLoading }] = useLazyGetCheckoutCartQuery()

  /**
   * 获取购物车信息
   */
  const fetchCheckoutCart = useCallback(async () => {
    try {
      const response = await getCheckoutCartQuery({}).unwrap()
      if (response?.customer?.shipping_cart?.items) {
        dispatch(setAllCartProducts(formatProducts(response?.customer.shipping_cart.items as TCheckoutCartProductItems)))
      }
    } catch (error) {
      // 不提示错误信息
      console.error('useCheckoutCart error:', error)
    }
  }, [dispatch, getCheckoutCartQuery])

  return { fetchCheckoutCart, isLoading }
}

export default useCheckoutCart
