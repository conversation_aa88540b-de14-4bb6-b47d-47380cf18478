#!/usr/bin/env node

/**
 * 高德地图安全代理测试脚本
 * 用于验证API代理功能是否正常工作
 */

const https = require('https')
const http = require('http')

// 测试配置
const TEST_CONFIG = {
  // 测试的应用端口
  WEB_PORT: 3000,
  H5_PORT: 3001,
  // 测试超时时间
  TIMEOUT: 10000,
  // 测试的API端点
  ENDPOINTS: {
    CONFIG: '/api/amap/config',
    PROXY: '/api/amap/proxy'
  }
}

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// HTTP请求工具
function makeRequest(options) {
  return new Promise((resolve, reject) => {
    const client = options.protocol === 'https:' ? https : http
    
    const req = client.request(options, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data)
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          })
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          })
        }
      })
    })
    
    req.on('error', reject)
    req.setTimeout(TEST_CONFIG.TIMEOUT, () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })
    
    req.end()
  })
}

// 测试配置API端点
async function testConfigEndpoint(port, appName) {
  log('blue', `\n=== 测试 ${appName} 配置端点 ===`)
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: port,
      path: TEST_CONFIG.ENDPOINTS.CONFIG,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'AMap-Security-Test/1.0'
      }
    })
    
    log('cyan', `状态码: ${response.status}`)
    
    if (response.status === 200) {
      log('green', '✓ 配置端点响应正常')
      
      // 验证响应结构
      if (response.data.success && response.data.data) {
        const config = response.data.data
        
        // 检查必要字段
        const requiredFields = ['version', 'plugins', 'securityJsCode', 'loaderUrl', 'apiKey', 'timestamp']
        const missingFields = requiredFields.filter(field => !config[field])
        
        if (missingFields.length === 0) {
          log('green', '✓ 配置数据结构正确')
          log('cyan', `  版本: ${config.version}`)
          log('cyan', `  插件: ${config.plugins.join(', ')}`)
          log('cyan', `  时间戳: ${new Date(config.timestamp).toISOString()}`)
          log('cyan', `  安全密钥: ${config.securityJsCode ? '已设置' : '未设置'}`)
          log('cyan', `  API密钥: ${config.apiKey ? '已设置' : '未设置'}`)
        } else {
          log('red', `✗ 配置数据缺少字段: ${missingFields.join(', ')}`)
        }
      } else {
        log('red', '✗ 配置响应格式错误')
      }
      
      // 检查缓存头
      if (response.headers['cache-control']) {
        log('green', `✓ 缓存头已设置: ${response.headers['cache-control']}`)
      } else {
        log('yellow', '⚠ 缓存头未设置')
      }
      
    } else {
      log('red', `✗ 配置端点返回错误状态: ${response.status}`)
      if (response.data.error) {
        log('red', `  错误信息: ${response.data.error}`)
      }
    }
    
  } catch (error) {
    log('red', `✗ 配置端点测试失败: ${error.message}`)
  }
}

// 测试频率限制
async function testRateLimit(port, appName) {
  log('blue', `\n=== 测试 ${appName} 频率限制 ===`)
  
  try {
    // 快速发送多个请求
    const requests = []
    for (let i = 0; i < 15; i++) {
      requests.push(makeRequest({
        hostname: 'localhost',
        port: port,
        path: TEST_CONFIG.ENDPOINTS.CONFIG,
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'AMap-RateLimit-Test/1.0'
        }
      }))
    }
    
    const responses = await Promise.all(requests)
    const rateLimitedResponses = responses.filter(r => r.status === 429)
    
    if (rateLimitedResponses.length > 0) {
      log('green', `✓ 频率限制正常工作，${rateLimitedResponses.length} 个请求被限制`)
      
      // 检查频率限制头
      const limitResponse = rateLimitedResponses[0]
      if (limitResponse.headers['x-ratelimit-limit']) {
        log('cyan', `  限制数量: ${limitResponse.headers['x-ratelimit-limit']}`)
        log('cyan', `  剩余数量: ${limitResponse.headers['x-ratelimit-remaining']}`)
        log('cyan', `  重置时间: ${limitResponse.headers['x-ratelimit-reset']}`)
      }
    } else {
      log('yellow', '⚠ 频率限制可能未生效（所有请求都成功）')
    }
    
  } catch (error) {
    log('red', `✗ 频率限制测试失败: ${error.message}`)
  }
}

// 测试代理端点
async function testProxyEndpoint(port, appName) {
  log('blue', `\n=== 测试 ${appName} 代理端点 ===`)
  
  try {
    // 测试有效的代理请求
    const testUrl = 'https://restapi.amap.com/v3/geocode/regeo'
    const response = await makeRequest({
      hostname: 'localhost',
      port: port,
      path: `${TEST_CONFIG.ENDPOINTS.PROXY}?url=${encodeURIComponent(testUrl)}&location=116.397428,39.90923`,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'AMap-Proxy-Test/1.0'
      }
    })
    
    log('cyan', `代理请求状态码: ${response.status}`)
    
    if (response.status === 200) {
      log('green', '✓ 代理端点响应正常')
    } else if (response.status === 403) {
      log('yellow', '⚠ 代理请求被拒绝（可能是端点限制）')
    } else {
      log('red', `✗ 代理端点返回错误: ${response.status}`)
    }
    
  } catch (error) {
    log('red', `✗ 代理端点测试失败: ${error.message}`)
  }
}

// 检查服务是否运行
async function checkService(port, appName) {
  try {
    await makeRequest({
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET'
    })
    return true
  } catch (error) {
    log('red', `✗ ${appName} 服务未运行 (端口 ${port})`)
    return false
  }
}

// 主测试函数
async function runTests() {
  log('magenta', '🚀 开始高德地图安全代理测试')
  log('cyan', '请确保已启动开发服务器: pnpm dev')
  
  // 测试Web应用
  if (await checkService(TEST_CONFIG.WEB_PORT, 'Web')) {
    await testConfigEndpoint(TEST_CONFIG.WEB_PORT, 'Web')
    await testRateLimit(TEST_CONFIG.WEB_PORT, 'Web')
    await testProxyEndpoint(TEST_CONFIG.WEB_PORT, 'Web')
  }
  
  // 测试H5应用
  if (await checkService(TEST_CONFIG.H5_PORT, 'H5')) {
    await testConfigEndpoint(TEST_CONFIG.H5_PORT, 'H5')
    await testRateLimit(TEST_CONFIG.H5_PORT, 'H5')
    await testProxyEndpoint(TEST_CONFIG.H5_PORT, 'H5')
  }
  
  log('magenta', '\n🎉 测试完成！')
  log('cyan', '\n如果测试失败，请检查：')
  log('cyan', '1. 环境变量是否正确设置 (AMAP_KEY, AMAP_SECURITY_CODE)')
  log('cyan', '2. 开发服务器是否正常运行')
  log('cyan', '3. Redis服务是否可用（可选）')
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    log('red', `测试运行失败: ${error.message}`)
    process.exit(1)
  })
}

module.exports = { runTests }
