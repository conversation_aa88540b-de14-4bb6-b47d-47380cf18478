import {
  CREATE_GUEST_CART,
  DELETE_CART_PRODUCTS,
  GET_CHECKOUT_CART,
  SELECT_CART_PRODUCTS,
  UPDATE_CART_PRODUCT_QTY,
} from '../../graphql'
import {
  CreateGuestCartMutation,
  CreateGuestCartMutationVariables,
  DeleteCartProductsMutation,
  DeleteCartProductsMutationVariables,
  GetCheckoutCartQuery,
  GetCheckoutCartQueryVariables,
  SelectCartProductsMutation,
  SelectCartProductsMutationVariables,
  UpdateCartProductQtyMutation,
  UpdateCartProductQtyMutationVariables,
} from '../../graphql/generated/graphql'
import rootApi from '../../utils/rootApi'

/**
 * cart 查询/操作相关 api
 */
const cartApi = rootApi.injectEndpoints({
  endpoints: (build) => ({
    /**
     * 创建游客购物车
     */
    createGuestCart: build.mutation<CreateGuestCartMutation, CreateGuestCartMutationVariables>({
      query: ({ input }) => ({
        document: CREATE_GUEST_CART,
        variables: { input },
      }),
      extraOptions: {
        isAuth: false,
      },
    }),
    /**
     * 获取购物车信息
     */
    getCheckoutCart: build.query<GetCheckoutCartQuery, GetCheckoutCartQueryVariables>({
      query: (variables) => ({
        document: GET_CHECKOUT_CART,
        variables,
        method: 'POST',
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 更新购物车产品数量
     */
    updateCartProductQty: build.mutation<
      UpdateCartProductQtyMutation,
      UpdateCartProductQtyMutationVariables
    >({
      query: (variables) => ({
        document: UPDATE_CART_PRODUCT_QTY,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 删除购物车产品
     */
    deleteCartProducts: build.mutation<
      DeleteCartProductsMutation,
      DeleteCartProductsMutationVariables
    >({
      query: (variables) => ({
        document: DELETE_CART_PRODUCTS,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 选中/取消选中 购车产品
     */
    selectCartProducts: build.mutation<
      SelectCartProductsMutation,
      SelectCartProductsMutationVariables
    >({
      query: (variables) => ({
        document: SELECT_CART_PRODUCTS,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
  }),
})

export const {
  useCreateGuestCartMutation,
  useGetCheckoutCartQuery,
  useLazyGetCheckoutCartQuery,
  useUpdateCartProductQtyMutation,
  useDeleteCartProductsMutation,
  useSelectCartProductsMutation,
} = cartApi

export default cartApi
