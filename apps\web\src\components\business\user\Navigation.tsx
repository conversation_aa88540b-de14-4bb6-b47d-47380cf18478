'use client'
import { usePathname } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { Menu } from 'antd'

import { Link } from '@/i18n/navigation'

import { Address, Cart, Coupon, User } from './icons'

const Navigation = () => {
  const pathname = usePathname()
  const getI18nString = useTranslations('Web')
  const menuItems = [
    {
      key: 'personal',
      icon: <User />,
      label: getI18nString('account_center'),
      path: '/customer/account',
    },
    {
      key: 'orders',
      icon: <Cart />,
      label: getI18nString('my_orders'),
      path: '/customer/orders',
    },
    {
      key: 'coupons',
      icon: <Coupon />,
      label: getI18nString('my_coupon'),
      path: '/customer/coupons',
    },
    {
      key: 'address',
      icon: <Address />,
      label: getI18nString('my_shipping_address'),
      path: '/customer/addresses',
    },
  ]

  const selectedKey = menuItems.find((item) => pathname.startsWith(item.path))?.key || ''

  return (
    <div className="sticky top-[72px] self-start">
      <Menu
        className="user-navigation !w-[288px] flex-none !border-none 2xl:!w-[348px]"
        mode="inline"
        selectedKeys={[selectedKey]}
        style={{
          background: '#fff',
          borderRadius: '20px',
          padding: '4px 16px',
          height: 'max-content',
        }}
        items={menuItems.map((item) => ({
          key: item.key,
          icon: item.icon,
          label: (
            <Link href={item.path} className="font-miSansMedium380 text-[16px] leading-[140%]">
              {item.label}
            </Link>
          ),
        }))}
      />
    </div>
  )
}

export default Navigation
