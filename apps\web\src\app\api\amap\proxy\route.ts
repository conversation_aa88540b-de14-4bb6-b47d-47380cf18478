import { NextRequest, NextResponse } from 'next/server'

/**
 * 高德地图API代理端点
 * 代理对高德地图API的请求，在服务器端添加安全密钥
 */

// 允许代理的高德地图API端点
const ALLOWED_AMAP_ENDPOINTS = [
  'https://webapi.amap.com/loader.js',
  'https://restapi.amap.com/v3/geocode/regeo',
  'https://restapi.amap.com/v3/geocode/geo',
  'https://restapi.amap.com/v3/place/text',
  'https://restapi.amap.com/v3/place/around',
  'https://restapi.amap.com/v3/direction/driving',
  'https://restapi.amap.com/v3/direction/walking',
  'https://restapi.amap.com/v3/direction/transit/integrated'
]

// 请求频率限制（简单的内存存储，生产环境建议使用Redis）
const requestCounts = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1分钟
const RATE_LIMIT_MAX_REQUESTS = 100 // 每分钟最多100个请求

function checkRateLimit(clientId: string): boolean {
  const now = Date.now()
  const record = requestCounts.get(clientId)
  
  if (!record || now > record.resetTime) {
    requestCounts.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }
  
  if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }
  
  record.count++
  return true
}

export async function GET(request: NextRequest) {
  return handleRequest(request, 'GET')
}

export async function POST(request: NextRequest) {
  return handleRequest(request, 'POST')
}

async function handleRequest(request: NextRequest, method: string) {
  try {
    const apiKey = process.env.AMAP_KEY
    
    if (!apiKey) {
      console.error('高德地图API Key缺失')
      return NextResponse.json(
        { error: '地图服务配置错误', code: 'API_KEY_MISSING' },
        { status: 500 }
      )
    }

    // 获取目标URL
    const targetUrl = request.nextUrl.searchParams.get('url')
    if (!targetUrl) {
      return NextResponse.json(
        { error: '缺少目标URL参数', code: 'MISSING_URL' },
        { status: 400 }
      )
    }

    // 验证目标URL是否在允许列表中
    const isAllowedEndpoint = ALLOWED_AMAP_ENDPOINTS.some(endpoint => 
      targetUrl.startsWith(endpoint)
    )
    
    if (!isAllowedEndpoint) {
      console.warn('尝试访问未授权的端点:', targetUrl)
      return NextResponse.json(
        { error: '不支持的API端点', code: 'UNAUTHORIZED_ENDPOINT' },
        { status: 403 }
      )
    }

    // 简单的频率限制
    const clientId = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    if (!checkRateLimit(clientId)) {
      return NextResponse.json(
        { error: '请求过于频繁', code: 'RATE_LIMIT_EXCEEDED' },
        { status: 429 }
      )
    }

    // 构建代理请求URL，添加API Key
    const proxyUrl = new URL(targetUrl)
    proxyUrl.searchParams.set('key', apiKey)
    
    // 复制原始请求的查询参数（除了url参数）
    request.nextUrl.searchParams.forEach((value, key) => {
      if (key !== 'url') {
        proxyUrl.searchParams.set(key, value)
      }
    })

    // 准备请求选项
    const requestOptions: RequestInit = {
      method,
      headers: {
        'User-Agent': 'Ninebot-Web-Proxy/1.0',
        'Accept': request.headers.get('accept') || 'application/json',
        'Accept-Language': request.headers.get('accept-language') || 'zh-CN,zh;q=0.9'
      }
    }

    // 如果是POST请求，复制请求体
    if (method === 'POST') {
      const contentType = request.headers.get('content-type')
      if (contentType) {
        requestOptions.headers = {
          ...requestOptions.headers,
          'Content-Type': contentType
        }
      }
      
      try {
        requestOptions.body = await request.text()
      } catch (error) {
        console.warn('读取请求体失败:', error)
      }
    }

    // 发送代理请求
    const response = await fetch(proxyUrl.toString(), requestOptions)
    
    if (!response.ok) {
      console.error('高德地图API请求失败:', {
        status: response.status,
        statusText: response.statusText,
        url: proxyUrl.toString()
      })
      
      return NextResponse.json(
        { 
          error: '地图服务请求失败', 
          code: 'UPSTREAM_ERROR',
          status: response.status 
        },
        { status: response.status }
      )
    }

    // 获取响应内容
    const contentType = response.headers.get('content-type') || ''
    let responseData: any

    if (contentType.includes('application/json')) {
      responseData = await response.json()
    } else if (contentType.includes('text/') || contentType.includes('application/javascript')) {
      responseData = await response.text()
    } else {
      responseData = await response.arrayBuffer()
    }

    // 创建代理响应
    const proxyResponse = new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText
    })

    // 复制重要的响应头
    const headersToProxy = [
      'content-type',
      'cache-control',
      'expires',
      'last-modified',
      'etag'
    ]

    headersToProxy.forEach(headerName => {
      const headerValue = response.headers.get(headerName)
      if (headerValue) {
        proxyResponse.headers.set(headerName, headerValue)
      }
    })

    // 添加安全头
    proxyResponse.headers.set('X-Content-Type-Options', 'nosniff')
    proxyResponse.headers.set('X-Frame-Options', 'SAMEORIGIN')

    return proxyResponse

  } catch (error) {
    console.error('代理请求处理失败:', error)
    
    return NextResponse.json(
      { 
        error: '代理服务内部错误', 
        code: 'PROXY_ERROR' 
      },
      { status: 500 }
    )
  }
}

// 支持CORS预检请求
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin')
  const allowedOrigins = process.env.AMAP_ALLOWED_ORIGINS?.split(',') || []
  
  const response = new NextResponse(null, { status: 200 })
  
  if (process.env.NODE_ENV === 'development') {
    response.headers.set('Access-Control-Allow-Origin', origin || '*')
  } else if (origin && allowedOrigins.some(allowed => origin.includes(allowed.trim()))) {
    response.headers.set('Access-Control-Allow-Origin', origin)
  }
  
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  response.headers.set('Access-Control-Max-Age', '86400')
  
  return response
}
