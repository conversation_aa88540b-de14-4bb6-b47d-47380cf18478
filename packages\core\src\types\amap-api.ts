/**
 * 高德地图API相关类型定义
 * 用于客户端和服务器端之间的接口一致性
 */

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  code?: string
}

// 高德地图配置类型
export interface AMapConfig {
  version: string
  plugins: string[]
  securityJsCode: string
  loaderUrl: string
  apiKey: string // 添加API Key字段
  timestamp: number
}

// 地图配置API响应类型
export interface AMapConfigResponse extends ApiResponse<AMapConfig> {}

// 代理请求参数类型
export interface ProxyRequestParams {
  url: string
  [key: string]: string | number | boolean
}

// 错误代码枚举
export enum AMapApiErrorCode {
  CONFIG_MISSING = 'CONFIG_MISSING',
  API_KEY_MISSING = 'API_KEY_MISSING',
  INVALID_ORIGIN = 'INVALID_ORIGIN',
  MISSING_URL = 'MISSING_URL',
  UNAUTHORIZED_ENDPOINT = 'UNAUTHORIZED_ENDPOINT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  UPSTREAM_ERROR = 'UPSTREAM_ERROR',
  PROXY_ERROR = 'PROXY_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
}

// API端点常量
export const AMAP_API_ENDPOINTS = {
  CONFIG: '/api/amap/config',
  PROXY: '/api/amap/proxy',
} as const

// 允许的高德地图API端点
export const ALLOWED_AMAP_ENDPOINTS = [
  'https://webapi.amap.com/loader.js',
  'https://restapi.amap.com/v3/geocode/regeo',
  'https://restapi.amap.com/v3/geocode/geo',
  'https://restapi.amap.com/v3/place/text',
  'https://restapi.amap.com/v3/place/around',
  'https://restapi.amap.com/v3/direction/driving',
  'https://restapi.amap.com/v3/direction/walking',
  'https://restapi.amap.com/v3/direction/transit/integrated',
] as const

// 频率限制配置
export const RATE_LIMIT_CONFIG = {
  WINDOW_MS: 60 * 1000, // 1分钟
  MAX_REQUESTS: 100, // 每分钟最多100个请求
} as const
