/**
 * 高德地图API频率限制工具
 * 支持基于IP、用户ID等多种限制策略
 */

import { getCacheManager, generateCacheKey } from './amap-cache'

// 频率限制配置
export interface RateLimitConfig {
  windowMs: number      // 时间窗口（毫秒）
  maxRequests: number   // 最大请求数
  keyGenerator?: (identifier: string) => string  // 自定义键生成器
}

// 频率限制结果
export interface RateLimitResult {
  allowed: boolean      // 是否允许请求
  remaining: number     // 剩余请求数
  resetTime: number     // 重置时间戳
  totalRequests: number // 总请求数限制
}

// 默认配置
const DEFAULT_CONFIG: RateLimitConfig = {
  windowMs: 60 * 1000,  // 1分钟
  maxRequests: 100,     // 100个请求
}

// 内存存储的频率限制器（备选方案）
class MemoryRateLimiter {
  private requests = new Map<string, { count: number; resetTime: number }>()
  
  async checkLimit(
    identifier: string, 
    config: RateLimitConfig = DEFAULT_CONFIG
  ): Promise<RateLimitResult> {
    const now = Date.now()
    const key = config.keyGenerator ? config.keyGenerator(identifier) : identifier
    const record = this.requests.get(key)
    
    // 如果没有记录或者已过期，创建新记录
    if (!record || now >= record.resetTime) {
      const newRecord = {
        count: 1,
        resetTime: now + config.windowMs
      }
      this.requests.set(key, newRecord)
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: newRecord.resetTime,
        totalRequests: config.maxRequests
      }
    }
    
    // 检查是否超过限制
    if (record.count >= config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.resetTime,
        totalRequests: config.maxRequests
      }
    }
    
    // 增加计数
    record.count++
    
    return {
      allowed: true,
      remaining: config.maxRequests - record.count,
      resetTime: record.resetTime,
      totalRequests: config.maxRequests
    }
  }
  
  async resetLimit(identifier: string, config: RateLimitConfig = DEFAULT_CONFIG): Promise<void> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : identifier
    this.requests.delete(key)
  }
}

// Redis频率限制器
class RedisRateLimiter {
  private cache = getCacheManager()
  
  async checkLimit(
    identifier: string, 
    config: RateLimitConfig = DEFAULT_CONFIG
  ): Promise<RateLimitResult> {
    const now = Date.now()
    const key = generateCacheKey('ratelimit', identifier)
    
    try {
      const cached = await this.cache.get(key)
      let record: { count: number; resetTime: number }
      
      if (!cached) {
        // 创建新记录
        record = {
          count: 1,
          resetTime: now + config.windowMs
        }
      } else {
        record = JSON.parse(cached)
        
        // 检查是否过期
        if (now >= record.resetTime) {
          record = {
            count: 1,
            resetTime: now + config.windowMs
          }
        } else {
          record.count++
        }
      }
      
      // 保存到缓存
      const ttlSeconds = Math.ceil(config.windowMs / 1000)
      await this.cache.set(key, JSON.stringify(record), ttlSeconds)
      
      return {
        allowed: record.count <= config.maxRequests,
        remaining: Math.max(0, config.maxRequests - record.count),
        resetTime: record.resetTime,
        totalRequests: config.maxRequests
      }
    } catch (error) {
      console.warn('Redis频率限制检查失败，允许请求:', error)
      // 如果Redis失败，允许请求通过
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: now + config.windowMs,
        totalRequests: config.maxRequests
      }
    }
  }
  
  async resetLimit(identifier: string): Promise<void> {
    const key = generateCacheKey('ratelimit', identifier)
    await this.cache.del(key)
  }
}

// 频率限制管理器
class RateLimitManager {
  private redisLimiter: RedisRateLimiter
  private memoryLimiter: MemoryRateLimiter
  
  constructor() {
    this.redisLimiter = new RedisRateLimiter()
    this.memoryLimiter = new MemoryRateLimiter()
  }
  
  async checkLimit(
    identifier: string, 
    config: RateLimitConfig = DEFAULT_CONFIG
  ): Promise<RateLimitResult> {
    try {
      // 优先使用Redis限制器
      return await this.redisLimiter.checkLimit(identifier, config)
    } catch (error) {
      console.warn('Redis频率限制失败，使用内存限制器:', error)
      // 备选使用内存限制器
      return await this.memoryLimiter.checkLimit(identifier, config)
    }
  }
  
  async resetLimit(identifier: string, config: RateLimitConfig = DEFAULT_CONFIG): Promise<void> {
    await Promise.all([
      this.redisLimiter.resetLimit(identifier),
      this.memoryLimiter.resetLimit(identifier, config)
    ])
  }
}

// 单例管理器
let rateLimitManager: RateLimitManager | null = null

export const getRateLimitManager = (): RateLimitManager => {
  if (!rateLimitManager) {
    rateLimitManager = new RateLimitManager()
  }
  return rateLimitManager
}

// 预定义的限制配置
export const RATE_LIMIT_CONFIGS = {
  // API配置请求限制（较宽松）
  CONFIG: {
    windowMs: 5 * 60 * 1000,  // 5分钟
    maxRequests: 10,          // 10个请求
  },
  
  // API代理请求限制（中等）
  PROXY: {
    windowMs: 60 * 1000,      // 1分钟
    maxRequests: 100,         // 100个请求
  },
  
  // 地理编码请求限制（严格）
  GEOCODING: {
    windowMs: 60 * 1000,      // 1分钟
    maxRequests: 50,          // 50个请求
  },
  
  // 搜索请求限制（严格）
  SEARCH: {
    windowMs: 60 * 1000,      // 1分钟
    maxRequests: 30,          // 30个请求
  }
} as const

// 便捷方法
export const amapRateLimit = {
  // 检查配置请求限制
  async checkConfigLimit(identifier: string): Promise<RateLimitResult> {
    const manager = getRateLimitManager()
    return manager.checkLimit(identifier, RATE_LIMIT_CONFIGS.CONFIG)
  },
  
  // 检查代理请求限制
  async checkProxyLimit(identifier: string): Promise<RateLimitResult> {
    const manager = getRateLimitManager()
    return manager.checkLimit(identifier, RATE_LIMIT_CONFIGS.PROXY)
  },
  
  // 检查地理编码限制
  async checkGeocodingLimit(identifier: string): Promise<RateLimitResult> {
    const manager = getRateLimitManager()
    return manager.checkLimit(identifier, RATE_LIMIT_CONFIGS.GEOCODING)
  },
  
  // 检查搜索限制
  async checkSearchLimit(identifier: string): Promise<RateLimitResult> {
    const manager = getRateLimitManager()
    return manager.checkLimit(identifier, RATE_LIMIT_CONFIGS.SEARCH)
  },
  
  // 重置限制
  async resetLimit(identifier: string, type: keyof typeof RATE_LIMIT_CONFIGS = 'PROXY'): Promise<void> {
    const manager = getRateLimitManager()
    return manager.resetLimit(identifier, RATE_LIMIT_CONFIGS[type])
  }
}

// 获取客户端标识符的工具函数
export const getClientIdentifier = (request: Request): string => {
  // 优先使用真实IP
  const forwardedFor = request.headers.get('x-forwarded-for')
  if (forwardedFor) {
    return forwardedFor.split(',')[0].trim()
  }
  
  // 备选使用其他IP头
  const realIp = request.headers.get('x-real-ip')
  if (realIp) {
    return realIp
  }
  
  // 最后使用User-Agent作为标识（不太可靠）
  const userAgent = request.headers.get('user-agent')
  if (userAgent) {
    return `ua:${Buffer.from(userAgent).toString('base64').slice(0, 16)}`
  }
  
  return 'unknown'
}
