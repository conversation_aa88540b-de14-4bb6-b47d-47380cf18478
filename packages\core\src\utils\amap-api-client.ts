/**
 * 高德地图API客户端工具
 * 用于从客户端安全地调用高德地图服务
 */

import type { 
  AMapConfig, 
  AMapConfigResponse, 
  ApiResponse,
  ProxyRequestParams 
} from '../types/amap-api'
import { AMAP_API_ENDPOINTS, AMapApiErrorCode } from '../types/amap-api'

// API客户端错误类
export class AMapApiError extends Error {
  constructor(
    message: string,
    public code: string,
    public status?: number
  ) {
    super(message)
    this.name = 'AMapApiError'
  }
}

// HTTP请求工具函数
async function fetchWithTimeout(
  url: string, 
  options: RequestInit = {}, 
  timeout = 10000
): Promise<Response> {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    })
    clearTimeout(timeoutId)
    return response
  } catch (error) {
    clearTimeout(timeoutId)
    throw error
  }
}

// 处理API响应
async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`
    let errorCode = 'HTTP_ERROR'
    
    try {
      const errorData = await response.json()
      if (errorData.error) {
        errorMessage = errorData.error
      }
      if (errorData.code) {
        errorCode = errorData.code
      }
    } catch {
      // 如果无法解析错误响应，使用默认错误信息
    }
    
    throw new AMapApiError(errorMessage, errorCode, response.status)
  }
  
  try {
    const data = await response.json()
    
    if (!data.success && data.error) {
      throw new AMapApiError(
        data.error, 
        data.code || AMapApiErrorCode.INTERNAL_ERROR
      )
    }
    
    return data
  } catch (error) {
    if (error instanceof AMapApiError) {
      throw error
    }
    
    throw new AMapApiError(
      '响应数据解析失败', 
      AMapApiErrorCode.INTERNAL_ERROR
    )
  }
}

/**
 * 获取高德地图配置
 * @returns Promise<AMapConfig> 地图配置信息
 */
export async function getAMapConfig(): Promise<AMapConfig> {
  try {
    const response = await fetchWithTimeout(AMAP_API_ENDPOINTS.CONFIG, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })
    
    const result: AMapConfigResponse = await handleApiResponse(response)
    
    if (!result.data) {
      throw new AMapApiError(
        '地图配置数据缺失', 
        AMapApiErrorCode.INTERNAL_ERROR
      )
    }
    
    return result.data
  } catch (error) {
    console.error('获取地图配置失败:', error)
    
    if (error instanceof AMapApiError) {
      throw error
    }
    
    throw new AMapApiError(
      '获取地图配置失败', 
      AMapApiErrorCode.INTERNAL_ERROR
    )
  }
}

/**
 * 通过代理调用高德地图API
 * @param targetUrl 目标API URL
 * @param params 请求参数
 * @param options 请求选项
 * @returns Promise<any> API响应数据
 */
export async function proxyAMapRequest(
  targetUrl: string,
  params: Record<string, any> = {},
  options: RequestInit = {}
): Promise<any> {
  try {
    // 构建代理请求URL
    const proxyUrl = new URL(AMAP_API_ENDPOINTS.PROXY, window.location.origin)
    proxyUrl.searchParams.set('url', targetUrl)
    
    // 添加其他参数
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        proxyUrl.searchParams.set(key, String(value))
      }
    })
    
    const response = await fetchWithTimeout(proxyUrl.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    // 对于代理请求，直接返回响应数据，不需要包装
    if (response.headers.get('content-type')?.includes('application/json')) {
      return await response.json()
    } else {
      return await response.text()
    }
    
  } catch (error) {
    console.error('代理API请求失败:', error)
    
    if (error instanceof AMapApiError) {
      throw error
    }
    
    throw new AMapApiError(
      '代理API请求失败', 
      AMapApiErrorCode.PROXY_ERROR
    )
  }
}

/**
 * 检查API服务是否可用
 * @returns Promise<boolean> 服务是否可用
 */
export async function checkAMapApiHealth(): Promise<boolean> {
  try {
    await getAMapConfig()
    return true
  } catch (error) {
    console.warn('高德地图API服务不可用:', error)
    return false
  }
}

// 导出错误类型，方便外部使用
export { AMapApiErrorCode } from '../types/amap-api'
