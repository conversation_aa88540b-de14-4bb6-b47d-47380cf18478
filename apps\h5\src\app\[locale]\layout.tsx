import { headers } from 'next/headers'
import { notFound } from 'next/navigation'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import { miSansFamilyVariable } from '@public/fonts'
import {
  AuthProvider,
  GlobalProvider,
  LoadingProvider,
  QUID_KEY,
  StoreProvider,
  ToastProvider,
} from '@ninebot/core'
import { TBaseComponentProps } from '@ninebot/core/src/typings'
import clsx from 'clsx'
import { getLangDir } from 'rtl-detect'

import { i18nRouting } from '@/config'
import { TLocales } from '@/i18n/type'

type TLocalLayoutProps = TBaseComponentProps<{
  params: { locale: string }
}>

/**
 * 局部布局组件
 */
const LocalLayout = async ({ children, params }: TLocalLayoutProps) => {
  const { locale } = params

  const headersList = headers()
  // 从 header 中获取 quid
  const quid = headersList.get(QUID_KEY.toUpperCase())
  const pathName = headersList.get('x-url') || ''

  // Ensure that the incoming `locale` is valid
  if (!i18nRouting.locales.includes(locale as TLocales)) {
    notFound()
  }

  // Detect the language direction based on the locale
  const direction = getLangDir(locale)

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages()

  // 使用纯页面，去除 header/footer 内容
  const isPurePage = ['/limit'].includes(pathName)

  return (
    <html lang={locale} dir={direction}>
      <body
        className={clsx(
          miSansFamilyVariable,
          'h-full scroll-smooth font-miSansMedium380 text-base text-black',
        )}>
        <div id="app" className="h-full">
          <NextIntlClientProvider messages={messages}>
            <StoreProvider quid={quid ? quid : ''}>
              <GlobalProvider isPurePage={isPurePage}>
                <LoadingProvider>
                  <ToastProvider position="center">
                    <AuthProvider>{children}</AuthProvider>
                  </ToastProvider>
                </LoadingProvider>
              </GlobalProvider>
            </StoreProvider>
          </NextIntlClientProvider>
        </div>
      </body>
    </html>
  )
}

export default LocalLayout
