'use client'
import { useEffect } from 'react'
import { useCheckoutCart, useVolcAnalytics } from '@ninebot/core/src/businessHooks'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { ROUTE, TRACK_EVENT } from '@ninebot/core/src/constants'
import { selectCartAllProductsTotal } from '@ninebot/core/src/store'
import { useAppSelector } from '@ninebot/core/src/store/hooks'

import { HeaderCart } from '@/components/icons'
interface CartItemCountProps {
  className?: string
  isLoggedIn: boolean
  children?: React.ReactNode
  isPdp?: boolean
  sku?: string
}

const CartItemCount = ({
  className = '',
  isLoggedIn,
  children,
  isPdp = false,
  sku,
}: CartItemCountProps) => {
  const totalCount = useAppSelector(selectCartAllProductsTotal) || 0
  const { fetchCheckoutCart } = useCheckoutCart()
  const { reportEvent } = useVolcAnalytics()

  const { openPage } = useNavigate()
  const handleGoCheckoutCart = () => {
    if (isPdp) {
      reportEvent(TRACK_EVENT.shop_details_cart_button_click, {
        sku_id: sku,
      })
    } else {
      reportEvent(TRACK_EVENT.shop_homepage_cart_button_click, {
        button_id: 'shop_my_cart',
      })
    }
    openPage({
      route: ROUTE.checkoutCart,
    })
  }

  // 加载页面时获取购物车数据
  useEffect(() => {
    if (!isLoggedIn) return

    fetchCheckoutCart()

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchCheckoutCart()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [fetchCheckoutCart, isLoggedIn])

  // 计算徽章显示内容和样式
  const badgeContent = isLoggedIn && totalCount > 0 ? (totalCount > 99 ? '99+' : totalCount) : ''
  const showBadge = !!badgeContent

  // 根据数量计算徽章尺寸
  const getBadgeSize = () => {
    if (!isLoggedIn || totalCount === 0) return { width: 0, height: 0 }
    if (totalCount > 99) return { width: 24, height: 24 }
    if (totalCount > 9) return { width: 20, height: 20 }
    return { width: 16, height: 16 }
  }

  const badgeSize = getBadgeSize()

  return (
    <button onClick={handleGoCheckoutCart} className="flex flex-col items-center">
      <div
        className="relative"
        style={{
          height: '24px',
        }}>
        <HeaderCart className={className} />
        {showBadge && (
          <div
            className="absolute flex items-center justify-center rounded-full text-xs font-medium text-white"
            style={{
              right: '-30%',
              top: '-30%',
              backgroundColor: '#DA291C',
              width: `${badgeSize.width}px`,
              height: `${badgeSize.height}px`,
              fontSize: totalCount > 99 ? '10px' : totalCount > 9 ? '11px' : '12px',
              lineHeight: '1',
              minWidth: `${badgeSize.width}px`,
              zIndex: 10,
            }}>
            {badgeContent}
          </div>
        )}
      </div>

      {children}
    </button>
  )
}

export default CartItemCount
