/**
 * 自定义 GraphQL 错误类
 */
class GqlError extends Error {
  status: number
  data?: unknown
  type?: string
  constructor(message: string, status: number, data?: unknown, type = 'default') {
    super(message)
    this.name = this.constructor.name // Set the error name to the name of the class
    this.status = status
    this.data = data
    this.type = type; // 自定义 error type
  }
}

export default GqlError
