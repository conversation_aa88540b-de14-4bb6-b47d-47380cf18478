import { useCallback, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  IconPlus,
  NCoinView,
  Price,
  resolveCatchMessage,
  sleep,
  TCatchMessage,
  TRACK_EVENT,
  useLoadingContext,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useDebounceFn } from '@ninebot/core/src/businessHooks'
import {
  useAddCartItemsToCheckoutMutation,
  useSelectCartProductsMutation,
} from '@ninebot/core/src/services'
import {
  cartIsExcludeSelectedProductsPureNCoinSelector,
  cartIsIncludeSelectedProductsPureNCoinSelector,
  cartSelectedProductsPureNCoinTotalSelector,
  cartSelectedProductsSelector,
  cartSelectedProductsTotalPriceSelector,
  cartSelectedProductsUidSelector,
  deleteCartDeleteProductUIDs,
  deselectedCartProducts,
} from '@ninebot/core/src/store'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { Button } from 'antd'

import { CustomImage, EmptyCart, IconArrow } from '@/components'
import { useRouter } from '@/i18n/navigation'

const CartSummary = () => {
  const getI18nString = useTranslations('Common')
  const { reportEvent } = useVolcAnalytics()

  const toast = useToastContext()
  const loading = useLoadingContext()
  const router = useRouter()
  const dispatch = useAppDispatch()
  const cartSelectedProductsTotalPrice = useAppSelector(cartSelectedProductsTotalPriceSelector)
  const selectedProducts = useAppSelector(cartSelectedProductsSelector)
  const selectedProductsUid = useAppSelector(cartSelectedProductsUidSelector)

  const isExcludeSelectedProductsPureNCoin = useAppSelector(
    cartIsExcludeSelectedProductsPureNCoinSelector,
  )
  const isIncludeSelectedProductsPureNCoin = useAppSelector(
    cartIsIncludeSelectedProductsPureNCoinSelector,
  )
  const selectedProductsPureNCoinTotal = useAppSelector(cartSelectedProductsPureNCoinTotalSelector)

  const [addCartItemsToCheckoutMutation, { isLoading: addCartItemsToCheckoutLoading }] =
    useAddCartItemsToCheckoutMutation()
  const [selectCartProductsMutation] = useSelectCartProductsMutation()

  // 记录当前鼠标悬停的商品ID
  const [hoveredProductId, setHoveredProductId] = useState<string | null>(null)
  // 是否展开显示所有商品
  const [expandProducts, setExpandProducts] = useState(false)

  /**
   * 结算按钮是否 disabled
   */
  const isDisabledCheckoutBtn = useMemo(() => {
    return addCartItemsToCheckoutLoading || !selectedProducts || selectedProducts.length === 0
  }, [addCartItemsToCheckoutLoading, selectedProducts])

  /**
   * 点击结算按钮事件
   */
  const { run: handleCheckout } = useDebounceFn(async () => {
    if (!selectedProductsUid.length) {
      toast.show({ icon: 'info', content: getI18nString('please_select_product') })
      return
    }

    reportEvent(TRACK_EVENT.shop_cart_checkout_click, {
      button_id: 'shop_checkout',
    })

    try {
      loading.show()
      const response = await addCartItemsToCheckoutMutation({
        itemIds: selectedProductsUid,
      }).unwrap()

      const result = response?.addCartItemToCheckout

      if (result?.status && result?.cart?.id) {
        await sleep(500)
        loading.hide()
        await sleep(500)
        router.push('/checkout')
        // openPage({
        //   route: ROUTE.checkout,
        // });
      }

      if (result?.add_to_checkout_user_errors?.length) {
        await sleep(500)
        loading.hide()
        toast.show({
          icon: 'fail',
          content: result?.add_to_checkout_user_errors?.[0]?.message || '',
        })
      }
    } catch (error) {
      await sleep(500)
      loading.hide()
      await sleep(500)
      toast.show({ icon: 'fail', content: resolveCatchMessage(error as TCatchMessage) as string })
    }
  })

  /**
   * 取消选中商品
   */
  const handleUnselectProduct = useCallback(
    async (uid: string) => {
      try {
        loading.show()
        const response = await selectCartProductsMutation({
          items: [
            {
              item_id: uid,
              is_selected: 0,
            },
          ],
        }).unwrap()

        if (response?.selectProductsInShippingCart) {
          dispatch(deselectedCartProducts([uid]))
          dispatch(deleteCartDeleteProductUIDs([uid]))
          await sleep(500)
          loading.hide()
          toast.show({ icon: 'success', content: '已取消结算该商品' })
        }
      } catch (error) {
        await sleep(500)
        loading.hide()
        await sleep(500)
        toast.show({ icon: 'fail', content: String(resolveCatchMessage(error as TCatchMessage)) })
      }
    },
    [dispatch, loading, selectCartProductsMutation, toast],
  )

  // 显示已选商品图片
  const renderProductImages = () => {
    if (!selectedProducts || selectedProducts.length === 0) {
      return (
        <div className="flex h-[200px] items-center justify-center border-b border-gray-base">
          <div className="flex flex-col items-center justify-center gap-base">
            <EmptyCart size={108} />
            <span className="text-base text-gray-3">还没有待结算的商品</span>
          </div>
        </div>
      )
    }

    const totalProducts = selectedProducts.length
    const displayCount = expandProducts ? totalProducts : 8
    const displayText = totalProducts > 8 ? `已选${totalProducts}件商品` : null

    // 切换展开收起状态
    const toggleExpand = () => {
      if (totalProducts > 8) {
        setExpandProducts(!expandProducts)
      }
    }

    return (
      <div className="overflow-hidden border-b border-gray-base pb-base-12">
        <div
          className={`my-[6px] grid grid-cols-4 gap-base-12 ${expandProducts ? 'max-h-none' : 'max-h-[200px]'} overflow-hidden transition-all duration-300`}>
          {selectedProducts.slice(0, displayCount).map((product) => (
            <div
              key={product.uid}
              className="relative aspect-square w-[72px] overflow-hidden rounded-xl bg-gray-100 xl:w-[clamp(72px,72px+((100vw-1024px)/416)*22,94px)]"
              onMouseEnter={() => setHoveredProductId(product.uid)}
              onMouseLeave={() => setHoveredProductId(null)}>
              <CustomImage
                displayMode="responsive"
                aspectRatio="1/1"
                src={product.rawProduct?.product?.image?.url || ''}
                alt={product.rawProduct?.product?.name || '商品图片'}
                className="rounded-xl"
                priority
              />
              {hoveredProductId === product.uid && (
                <button
                  onClick={() => {
                    handleUnselectProduct(product.uid)
                  }}
                  className="absolute right-0 top-0 rounded-bl-xl rounded-tr-xl bg-[#D1D1D4] px-3 py-2 text-sm text-[#444446] hover:bg-white">
                  取消结算
                </button>
              )}
            </div>
          ))}
        </div>
        {displayText && (
          <div className="flex w-full justify-center">
            <button
              className="flex items-center justify-center gap-[4px] px-base-16 py-base"
              onClick={toggleExpand}>
              <span className="text-base">{displayText}</span>
              <IconArrow rotate={expandProducts ? 180 : 0} />
            </button>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="w-full max-w-[368px] 2xl:max-w-[380px] 3xl:max-w-[412px]">
      <div className="sticky top-[76px] z-10 bg-white">
        <div className="my-base-24">
          <h4 className="font-miSansSemibold520 text-[22px] leading-none">结算明细</h4>
          <div className="mt-[8px] text-sm leading-none text-gray-3">实际优惠金额以下单页为准</div>
        </div>
        {renderProductImages()}
        {selectedProducts.length > 0 && (
          <div className="flex items-center justify-between py-base-16">
            <span className="font-miSansSemibold520 text-lg leading-none text-[#0F0F0F]">
              商品总价
            </span>
            <div className="flex items-center">
              {(isExcludeSelectedProductsPureNCoin || !isIncludeSelectedProductsPureNCoin) && (
                <Price
                  price={{
                    value: cartSelectedProductsTotalPrice,
                  }}
                  color="primary"
                  currencyStyle="text-[14px] leading-none"
                  textStyle="text-[14px] leading-none"
                  fractionStyle="text-[14px] leading-none"
                  bold
                />
              )}

              {isIncludeSelectedProductsPureNCoin && (
                <div className="flex items-center">
                  {(isExcludeSelectedProductsPureNCoin || !isIncludeSelectedProductsPureNCoin) && (
                    <div className="mx-2">
                      <IconPlus color="#000000" size={12} />
                    </div>
                  )}

                  <NCoinView
                    number={selectedProductsPureNCoinTotal}
                    textStyle="text-[14px]"
                    iconStyle={{ size: 14 }}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        <div className="py-base-24">
          <div className="flex items-center justify-end">
            <span className="font-miSansSemibold520 text-base leading-none text-[#444446]">
              合计：
            </span>
            <div className="flex items-center">
              {(isExcludeSelectedProductsPureNCoin || !isIncludeSelectedProductsPureNCoin) && (
                <Price
                  price={{
                    value: cartSelectedProductsTotalPrice,
                  }}
                  color="primary"
                  currencyStyle="text-[20px] leading-none"
                  textStyle="text-[20px] leading-none"
                  fractionStyle="text-[20px] leading-none"
                  bold
                />
              )}

              {isIncludeSelectedProductsPureNCoin && (
                <div className="flex items-center">
                  {(isExcludeSelectedProductsPureNCoin || !isIncludeSelectedProductsPureNCoin) && (
                    <div className="mx-2">
                      <IconPlus color="#000000" size={14} />
                    </div>
                  )}

                  <NCoinView
                    number={selectedProductsPureNCoinTotal}
                    textStyle="text-[20px]"
                    iconStyle={{ size: 20 }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        <Button
          type="primary"
          className="w-full"
          onClick={handleCheckout}
          size="large"
          disabled={isDisabledCheckoutBtn}>
          去结算
        </Button>
      </div>
    </div>
  )
}

export default CartSummary
