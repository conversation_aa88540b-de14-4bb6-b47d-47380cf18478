# 高德地图安全密钥迁移指南

## 概述

为了增强安全性，我们将高德地图的API密钥和安全密钥从客户端迁移到服务器端。这样可以避免将密钥直接暴露给Web端代码，减少密钥被滥用的风险。

## 变更内容

### 环境变量变更

| 原变量名 | 新变量名 | 说明 |
|---------|---------|------|
| `NEXT_PUBLIC_AMAP_KEY` | `AMAP_KEY` | 高德地图API Key，现在仅在服务器端使用 |
| `NEXT_PUBLIC_AMAP_SECURITY_CODE` | `AMAP_SECURITY_CODE` | 高德地图安全密钥，现在仅在服务器端使用 |
| - | `AMAP_ALLOWED_ORIGINS` | 新增：允许的请求来源，用于CORS控制 |

### 架构变更

```
之前的架构：
客户端 → 直接调用高德地图API (密钥暴露)

现在的架构：
客户端 → Next.js API代理 → 高德地图API (密钥安全)
```

## 迁移步骤

### 1. 更新环境变量

#### 开发环境 (.env.development)
```bash
# 删除或注释掉旧的变量
# NEXT_PUBLIC_AMAP_KEY=your_old_key
# NEXT_PUBLIC_AMAP_SECURITY_CODE=your_old_security_code

# 添加新的服务器端变量
AMAP_KEY=your_amap_api_key_here
AMAP_SECURITY_CODE=your_amap_security_code_here
AMAP_ALLOWED_ORIGINS=localhost:3000,localhost:3001
```

#### 生产环境 (.env.production)
```bash
# 删除或注释掉旧的变量
# NEXT_PUBLIC_AMAP_KEY=your_old_key
# NEXT_PUBLIC_AMAP_SECURITY_CODE=your_old_security_code

# 添加新的服务器端变量
AMAP_KEY=your_production_amap_api_key
AMAP_SECURITY_CODE=your_production_amap_security_code
AMAP_ALLOWED_ORIGINS=your-domain.com,your-cdn-domain.com
```

### 2. 验证迁移

#### 检查API端点
1. 启动开发服务器：`pnpm dev`
2. 访问配置端点：`http://localhost:3000/api/amap/config`
3. 应该返回类似以下的响应：
```json
{
  "success": true,
  "data": {
    "version": "2.0",
    "plugins": ["AMap.Geolocation", "AMap.Geocoder", "AMap.Scale"],
    "securityJsCode": "your_security_code",
    "loaderUrl": "https://webapi.amap.com/loader.js",
    "timestamp": 1234567890
  }
}
```

#### 测试地图功能
1. 访问包含地图的页面
2. 检查浏览器控制台是否有错误
3. 验证定位和地理编码功能是否正常

### 3. 部署注意事项

#### PM2部署
确保在PM2配置中正确设置环境变量：
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'ninebot-web',
    script: 'next',
    args: 'start',
    env: {
      NODE_ENV: 'production',
      // 其他环境变量会从 .env.production 文件中加载
    }
  }]
}
```

#### Nginx配置
如果使用Nginx作为反向代理，确保API路由正确转发：
```nginx
location /api/amap/ {
    proxy_pass http://localhost:3000/api/amap/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}
```

## 安全特性

### 1. 密钥隐藏
- API密钥和安全密钥不再暴露给客户端
- 所有密钥仅在服务器端使用

### 2. 请求验证
- 验证请求来源（Origin/Referer检查）
- 支持配置允许的域名列表

### 3. 频率限制
- 内置简单的频率限制机制
- 防止API滥用

### 4. 错误处理
- 统一的错误响应格式
- 详细的服务器端日志记录

## 故障排除

### 常见问题

#### 1. 配置端点返回500错误
**原因**：环境变量未正确设置
**解决**：检查 `AMAP_KEY` 和 `AMAP_SECURITY_CODE` 是否正确配置

#### 2. CORS错误
**原因**：请求来源不在允许列表中
**解决**：
- 开发环境：确保 `NODE_ENV=development`
- 生产环境：在 `AMAP_ALLOWED_ORIGINS` 中添加你的域名

#### 3. 地图加载失败
**原因**：客户端代码未正确调用新的API
**解决**：确保使用了更新后的 `amap.ts` 文件

#### 4. 代理请求失败
**原因**：目标URL不在允许列表中
**解决**：检查请求的URL是否在 `ALLOWED_AMAP_ENDPOINTS` 中

### 调试技巧

1. **查看服务器日志**：
```bash
# 开发环境
pnpm dev

# 生产环境
pnpm pm2:list
pm2 logs your-app-name
```

2. **测试API端点**：
```bash
# 测试配置端点
curl http://localhost:3000/api/amap/config

# 测试代理端点
curl "http://localhost:3000/api/amap/proxy?url=https://restapi.amap.com/v3/geocode/regeo&location=116.397428,39.90923"
```

3. **检查环境变量**：
```javascript
// 在服务器端代码中临时添加
console.log('AMAP_KEY:', !!process.env.AMAP_KEY)
console.log('AMAP_SECURITY_CODE:', !!process.env.AMAP_SECURITY_CODE)
```

## 性能优化

### 1. 缓存策略
- 配置信息缓存5分钟
- 可以根据需要调整缓存时间

### 2. Redis缓存（可选）
如果项目已配置Redis，可以启用更高级的缓存：
```javascript
// 在API路由中添加Redis缓存逻辑
const cachedConfig = await redis.get('amap:config')
if (cachedConfig) {
  return JSON.parse(cachedConfig)
}
```

### 3. 请求优化
- 合并多个API请求
- 使用适当的HTTP缓存头

## 监控和告警

建议添加以下监控：

1. **API响应时间监控**
2. **错误率监控**
3. **频率限制触发监控**
4. **密钥使用量监控**

## 回滚计划

如果需要回滚到旧版本：

1. 恢复环境变量：
```bash
NEXT_PUBLIC_AMAP_KEY=your_key
NEXT_PUBLIC_AMAP_SECURITY_CODE=your_security_code
```

2. 恢复 `packages/core/src/utils/amap.ts` 文件
3. 删除API路由文件
4. 重新部署应用

## 联系支持

如果在迁移过程中遇到问题，请联系开发团队或查看项目文档。
