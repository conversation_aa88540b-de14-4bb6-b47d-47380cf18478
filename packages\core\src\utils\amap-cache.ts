/**
 * 高德地图API缓存工具
 * 提供Redis缓存和内存缓存的统一接口
 */

// 内存缓存实现（作为Redis的备选方案）
class MemoryCache {
  private cache = new Map<string, { value: any; expiry: number }>()
  
  async get(key: string): Promise<string | null> {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }
    
    return typeof item.value === 'string' ? item.value : JSON.stringify(item.value)
  }
  
  async set(key: string, value: string, ttlSeconds: number = 300): Promise<void> {
    this.cache.set(key, {
      value,
      expiry: Date.now() + (ttlSeconds * 1000)
    })
  }
  
  async del(key: string): Promise<void> {
    this.cache.delete(key)
  }
  
  async exists(key: string): Promise<boolean> {
    const item = this.cache.get(key)
    if (!item) return false
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return false
    }
    
    return true
  }
}

// Redis缓存实现
class RedisCache {
  private redis: any = null
  
  constructor() {
    this.initRedis()
  }
  
  private async initRedis() {
    try {
      // 动态导入Redis，避免在没有Redis的环境中出错
      const { createClient } = await import('redis')
      
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379'
      const redisPassword = process.env.REDIS_PASSWORD
      
      this.redis = createClient({
        url: redisUrl,
        password: redisPassword,
        socket: {
          connectTimeout: 5000,
          lazyConnect: true
        }
      })
      
      this.redis.on('error', (err: Error) => {
        console.warn('Redis连接错误，将使用内存缓存:', err.message)
        this.redis = null
      })
      
      await this.redis.connect()
      console.log('Redis缓存已连接')
    } catch (error) {
      console.warn('Redis初始化失败，将使用内存缓存:', error)
      this.redis = null
    }
  }
  
  async get(key: string): Promise<string | null> {
    if (!this.redis) return null
    
    try {
      return await this.redis.get(key)
    } catch (error) {
      console.warn('Redis GET操作失败:', error)
      return null
    }
  }
  
  async set(key: string, value: string, ttlSeconds: number = 300): Promise<void> {
    if (!this.redis) return
    
    try {
      await this.redis.setEx(key, ttlSeconds, value)
    } catch (error) {
      console.warn('Redis SET操作失败:', error)
    }
  }
  
  async del(key: string): Promise<void> {
    if (!this.redis) return
    
    try {
      await this.redis.del(key)
    } catch (error) {
      console.warn('Redis DEL操作失败:', error)
    }
  }
  
  async exists(key: string): Promise<boolean> {
    if (!this.redis) return false
    
    try {
      const result = await this.redis.exists(key)
      return result === 1
    } catch (error) {
      console.warn('Redis EXISTS操作失败:', error)
      return false
    }
  }
}

// 缓存管理器
class CacheManager {
  private redisCache: RedisCache
  private memoryCache: MemoryCache
  
  constructor() {
    this.redisCache = new RedisCache()
    this.memoryCache = new MemoryCache()
  }
  
  async get(key: string): Promise<string | null> {
    // 优先使用Redis缓存
    let value = await this.redisCache.get(key)
    if (value !== null) {
      return value
    }
    
    // 备选使用内存缓存
    return await this.memoryCache.get(key)
  }
  
  async set(key: string, value: string, ttlSeconds: number = 300): Promise<void> {
    // 同时设置Redis和内存缓存
    await Promise.all([
      this.redisCache.set(key, value, ttlSeconds),
      this.memoryCache.set(key, value, ttlSeconds)
    ])
  }
  
  async del(key: string): Promise<void> {
    await Promise.all([
      this.redisCache.del(key),
      this.memoryCache.del(key)
    ])
  }
  
  async exists(key: string): Promise<boolean> {
    const redisExists = await this.redisCache.exists(key)
    if (redisExists) return true
    
    return await this.memoryCache.exists(key)
  }
}

// 单例缓存管理器
let cacheManager: CacheManager | null = null

export const getCacheManager = (): CacheManager => {
  if (!cacheManager) {
    cacheManager = new CacheManager()
  }
  return cacheManager
}

// 缓存键生成器
export const generateCacheKey = (prefix: string, ...parts: string[]): string => {
  return `amap:${prefix}:${parts.join(':')}`
}

// 高德地图专用缓存方法
export const amapCache = {
  // 缓存地图配置
  async getConfig(): Promise<any | null> {
    const cache = getCacheManager()
    const key = generateCacheKey('config', 'main')
    const cached = await cache.get(key)
    return cached ? JSON.parse(cached) : null
  },
  
  async setConfig(config: any, ttlSeconds: number = 300): Promise<void> {
    const cache = getCacheManager()
    const key = generateCacheKey('config', 'main')
    await cache.set(key, JSON.stringify(config), ttlSeconds)
  },
  
  // 缓存API响应
  async getApiResponse(url: string, params: Record<string, any> = {}): Promise<any | null> {
    const cache = getCacheManager()
    const paramStr = Object.keys(params).sort().map(k => `${k}=${params[k]}`).join('&')
    const key = generateCacheKey('api', btoa(url), btoa(paramStr))
    const cached = await cache.get(key)
    return cached ? JSON.parse(cached) : null
  },
  
  async setApiResponse(
    url: string, 
    params: Record<string, any> = {}, 
    response: any, 
    ttlSeconds: number = 60
  ): Promise<void> {
    const cache = getCacheManager()
    const paramStr = Object.keys(params).sort().map(k => `${k}=${params[k]}`).join('&')
    const key = generateCacheKey('api', btoa(url), btoa(paramStr))
    await cache.set(key, JSON.stringify(response), ttlSeconds)
  },
  
  // 清除所有缓存
  async clearAll(): Promise<void> {
    const cache = getCacheManager()
    // 注意：这里只是示例，实际实现需要遍历所有相关键
    await Promise.all([
      cache.del(generateCacheKey('config', 'main')),
      // 可以添加更多特定的键清理
    ])
  }
}
