# 高德地图安全代理部署检查清单

## 部署前检查

### 1. 环境变量配置 ✅

确保以下环境变量已正确设置：

#### 开发环境 (.env.development)
```bash
# 高德地图配置
AMAP_KEY=your_development_amap_key
AMAP_SECURITY_CODE=your_development_security_code
AMAP_ALLOWED_ORIGINS=localhost:3000,localhost:3001

# 其他配置...
```

#### 生产环境 (.env.production)
```bash
# 高德地图配置
AMAP_KEY=your_production_amap_key
AMAP_SECURITY_CODE=your_production_security_code
AMAP_ALLOWED_ORIGINS=yourdomain.com,www.yourdomain.com

# Redis配置（可选）
REDIS_URL=redis://your-redis-server:6379
REDIS_PASSWORD=your_redis_password

# 其他配置...
```

### 2. 代码检查 ✅

- [ ] 确认所有 `NEXT_PUBLIC_AMAP_*` 环境变量已移除
- [ ] 确认客户端代码使用新的 `initializeAMap` 函数
- [ ] 确认API路由文件已正确创建
- [ ] 确认类型定义文件已更新

### 3. 依赖检查 ✅

确保以下依赖已安装：
```bash
# 检查Redis依赖（如果使用Redis缓存）
pnpm list redis

# 检查核心包依赖
pnpm list @ninebot/core
```

## 功能测试

### 1. 本地测试 ✅

```bash
# 启动开发服务器
pnpm dev

# 运行测试脚本
node scripts/test-amap-security.js
```

### 2. API端点测试 ✅

#### 配置端点测试
```bash
# Web应用
curl http://localhost:3000/api/amap/config

# H5应用
curl http://localhost:3001/api/amap/config
```

预期响应：
```json
{
  "success": true,
  "data": {
    "version": "2.0",
    "plugins": ["AMap.Geolocation", "AMap.Geocoder", "AMap.Scale"],
    "securityJsCode": "your_security_code",
    "loaderUrl": "https://webapi.amap.com/loader.js",
    "apiKey": "your_api_key",
    "timestamp": 1234567890
  }
}
```

#### 代理端点测试
```bash
curl "http://localhost:3000/api/amap/proxy?url=https://restapi.amap.com/v3/geocode/regeo&location=116.397428,39.90923"
```

### 3. 频率限制测试 ✅

```bash
# 快速发送多个请求测试频率限制
for i in {1..15}; do
  curl -s -o /dev/null -w "%{http_code}\n" http://localhost:3000/api/amap/config
done
```

应该看到一些 `429` 状态码。

### 4. 前端功能测试 ✅

- [ ] 地图初始化正常
- [ ] 定位功能正常
- [ ] 地理编码功能正常
- [ ] 地图显示正常
- [ ] 错误处理正常

## 生产部署

### 1. 构建检查 ✅

```bash
# 构建应用
pnpm build

# 检查构建输出
ls -la apps/web/.next/
ls -la apps/h5/.next/
```

### 2. 环境变量验证 ✅

```bash
# 验证生产环境变量
ENV_FILE=.env.production node -e "
require('dotenv').config({ path: '.env.production' });
console.log('AMAP_KEY:', !!process.env.AMAP_KEY);
console.log('AMAP_SECURITY_CODE:', !!process.env.AMAP_SECURITY_CODE);
console.log('AMAP_ALLOWED_ORIGINS:', process.env.AMAP_ALLOWED_ORIGINS);
"
```

### 3. PM2部署 ✅

```bash
# 部署Web应用
cd apps/web
pnpm deploy:master

# 部署H5应用
cd apps/h5
pnpm deploy:master

# 检查PM2状态
pnpm pm2:list
```

### 4. Nginx配置 ✅

确保Nginx正确配置API路由：

```nginx
# Web应用
location /api/amap/ {
    proxy_pass http://localhost:3000/api/amap/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# H5应用
location /h5/api/amap/ {
    proxy_pass http://localhost:3001/api/amap/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 生产验证

### 1. 健康检查 ✅

```bash
# 检查配置端点
curl https://yourdomain.com/api/amap/config

# 检查响应头
curl -I https://yourdomain.com/api/amap/config
```

### 2. 性能测试 ✅

```bash
# 使用ab进行简单的性能测试
ab -n 100 -c 10 https://yourdomain.com/api/amap/config
```

### 3. 安全验证 ✅

- [ ] 确认API密钥不在客户端代码中
- [ ] 确认频率限制正常工作
- [ ] 确认CORS设置正确
- [ ] 确认错误信息不泄露敏感信息

## 监控和维护

### 1. 日志监控 ✅

```bash
# 查看PM2日志
pm2 logs

# 查看Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 2. 性能监控 ✅

监控以下指标：
- API响应时间
- 错误率
- 频率限制触发次数
- 缓存命中率（如果使用Redis）

### 3. 定期检查 ✅

- [ ] 每周检查API使用量
- [ ] 每月检查错误日志
- [ ] 每季度更新高德地图SDK版本
- [ ] 定期检查安全配置

## 故障排除

### 常见问题

#### 1. 配置端点返回500错误
- 检查环境变量是否正确设置
- 检查服务器日志

#### 2. 地图加载失败
- 检查API密钥是否有效
- 检查网络连接
- 检查浏览器控制台错误

#### 3. 频率限制过于严格
- 调整频率限制配置
- 检查客户端标识符生成逻辑

#### 4. Redis连接失败
- 检查Redis服务状态
- 检查Redis连接配置
- 系统会自动降级到内存缓存

### 回滚计划

如果出现严重问题，可以快速回滚：

1. 恢复环境变量：
```bash
NEXT_PUBLIC_AMAP_KEY=your_key
NEXT_PUBLIC_AMAP_SECURITY_CODE=your_security_code
```

2. 恢复客户端代码
3. 重新部署

## 联系信息

如有问题，请联系：
- 开发团队：<EMAIL>
- 运维团队：<EMAIL>
- 紧急联系：<EMAIL>
