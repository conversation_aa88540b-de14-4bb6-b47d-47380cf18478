import type {
  AMapGeocoder,
  AMapGeolocation,
  AMapGeolocationOptions,
  AMapMap,
  AMapMapOptions,
} from '../types/amap'
import { getAMapConfig, AMapApiError } from './amap-api-client'
import type { AMapConfig } from '../types/amap-api'

// 错误类型定义
export class AMapError extends Error {
  constructor(
    message: string,
    public code: string,
  ) {
    super(message)
    this.name = 'AMapError'
  }
}

// 日志级别
type LogLevel = 'debug' | 'info' | 'warn' | 'error'

// 日志记录函数
const log = (level: LogLevel, message: string, ...args: unknown[]) => {
  const timestamp = new Date().toISOString()
  const prefix = `[AMap][${level.toUpperCase()}] ${timestamp}`

  switch (level) {
    case 'debug':
      console.debug(prefix, message, ...args)
      break
    case 'info':
      console.info(prefix, message, ...args)
      break
    case 'warn':
      console.warn(prefix, message, ...args)
      break
    case 'error':
      console.error(prefix, message, ...args)
      break
  }
}

// 全局状态
let isInitialized = false
let geolocation: AMapGeolocation | null = null
let geocoder: AMapGeocoder | null = null
let mapInstance: AMapMap | null = null
let initializationPromise: Promise<{
  geolocation: AMapGeolocation | null
  geocoder: AMapGeocoder | null
}> | null = null
let cachedConfig: AMapConfig | null = null

// 默认地理定位配置
const DEFAULT_GEOLOCATION_OPTIONS: AMapGeolocationOptions = {
  enableHighAccuracy: true,
  timeout: 10000,
  showButton: false,
  showMarker: false,
  showCircle: false,
}

export const initializeAMap = async (): Promise<{
  geolocation: AMapGeolocation | null
  geocoder: AMapGeocoder | null
}> => {
  if (isInitialized) {
    log('debug', '高德地图已初始化，返回现有实例')
    return { geolocation, geocoder }
  }

  // 如果正在初始化，返回同一个 Promise
  if (initializationPromise) {
    log('debug', '高德地图正在初始化中，等待完成')
    return initializationPromise
  }

  initializationPromise = (async () => {
    try {
      // 从API获取地图配置
      log('info', '开始获取高德地图配置')
      let config: AMapConfig

      try {
        config = await getAMapConfig()
        cachedConfig = config
        log('debug', '成功获取地图配置', { timestamp: config.timestamp })
      } catch (error) {
        const err =
          error instanceof AMapApiError
            ? new AMapError(error.message, error.code)
            : new AMapError('获取地图配置失败', 'CONFIG_FETCH_FAILED')
        log('error', err.message, error)
        return { geolocation: null, geocoder: null }
      }

      // 设置安全密钥
      window._AMapSecurityConfig = {
        securityJsCode: config.securityJsCode,
      }
      log('debug', '已设置安全密钥')

      // 检查是否已经加载了 AMapLoader
      if (!window.AMapLoader) {
        log('info', '开始加载 AMap Loader 脚本')
        await new Promise<void>((resolve, reject) => {
          const script = document.createElement('script')
          script.src = config.loaderUrl
          script.async = true
          script.onload = () => {
            log('info', 'AMap Loader 脚本加载完成')
            resolve()
          }
          script.onerror = (error) => {
            const err = new AMapError('AMap Loader 脚本加载失败', 'SCRIPT_LOAD_FAILED')
            log('error', err.message, error)
            reject(err)
          }
          document.head.appendChild(script)
        })
      }

      log('info', '开始初始化高德地图')
      const AMap = await window.AMapLoader.load({
        key: config.apiKey, // 使用从API获取的密钥
        version: config.version,
        plugins: config.plugins,
      })

      geolocation = new AMap.Geolocation(DEFAULT_GEOLOCATION_OPTIONS)
      geocoder = new AMap.Geocoder()
      isInitialized = true

      log('info', '高德地图初始化成功')
      return { geolocation, geocoder }
    } catch (error) {
      const err = new AMapError(
        error instanceof Error ? error.message : '高德地图初始化失败',
        'INITIALIZATION_FAILED',
      )
      log('error', err.message, error)
      return { geolocation: null, geocoder: null }
    }
  })()

  return initializationPromise
}

// 获取地图实例
export const getMapInstance = async (
  container: string,
  options: AMapMapOptions,
): Promise<AMapMap | null> => {
  if (!isInitialized) {
    await initializeAMap()
  }

  try {
    // 每次都创建新的地图实例
    mapInstance = new window.AMap.Map(container, options)
    mapInstance.addControl(new window.AMap.Scale())
    return mapInstance
  } catch (error) {
    console.error('创建地图实例失败:', error)
    return null
  }
}

// 重置高德地图实例（用于测试或重新初始化）
export const resetAMapInstance = () => {
  log('info', '重置高德地图实例')
  isInitialized = false
  geolocation = null
  geocoder = null
  mapInstance = null
  initializationPromise = null
  cachedConfig = null
}

// 获取缓存的配置信息（用于调试）
export const getCachedConfig = (): AMapConfig | null => {
  return cachedConfig
}

// 检查地图是否已初始化
export const isAMapInitialized = (): boolean => {
  return isInitialized
}
