import { NextRequest, NextResponse } from 'next/server'
import { amapCache } from '@ninebot/core/src/utils/amap-cache'
import { amapRateLimit, getClientIdentifier } from '@ninebot/core/src/utils/amap-rate-limiter'

/**
 * 高德地图配置API端点
 * 返回安全的地图初始化配置，不暴露真实的API密钥
 */

interface AMapConfig {
  version: string
  plugins: string[]
  securityJsCode: string
  loaderUrl: string
  apiKey: string
  timestamp: number
}

export async function GET(request: NextRequest) {
  try {
    // 频率限制检查
    const clientId = getClientIdentifier(request)
    const rateLimitResult = await amapRateLimit.checkConfigLimit(clientId)

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: '请求过于频繁，请稍后再试',
          code: 'RATE_LIMIT_EXCEEDED',
          resetTime: rateLimitResult.resetTime,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': rateLimitResult.totalRequests.toString(),
            'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
            'X-RateLimit-Reset': Math.ceil(rateLimitResult.resetTime / 1000).toString(),
          },
        },
      )
    }

    // 检查缓存
    const cachedConfig = await amapCache.getConfig()
    if (cachedConfig) {
      console.log('返回缓存的地图配置')
      return NextResponse.json(
        {
          success: true,
          data: cachedConfig,
        },
        {
          headers: {
            'Cache-Control': 'public, max-age=300, s-maxage=300',
            'X-Content-Type-Options': 'nosniff',
            'X-Cache': 'HIT',
          },
        },
      )
    }

    // 从服务器环境变量获取密钥（不暴露给客户端）
    const securityCode = process.env.AMAP_SECURITY_CODE
    const apiKey = process.env.AMAP_KEY

    if (!securityCode || !apiKey) {
      console.error('高德地图配置缺失:', {
        hasSecurityCode: !!securityCode,
        hasApiKey: !!apiKey,
      })

      return NextResponse.json(
        {
          error: '地图服务配置错误',
          code: 'CONFIG_MISSING',
        },
        { status: 500 },
      )
    }

    // 验证请求来源（基础安全检查）
    const origin = request.headers.get('origin')
    const referer = request.headers.get('referer')
    const allowedOrigins = process.env.AMAP_ALLOWED_ORIGINS?.split(',') || []

    // 在开发环境中放宽限制
    const isDevelopment = process.env.NODE_ENV === 'development'

    if (!isDevelopment && allowedOrigins.length > 0) {
      const isValidOrigin =
        origin && allowedOrigins.some((allowed) => origin.includes(allowed.trim()))
      const isValidReferer =
        referer && allowedOrigins.some((allowed) => referer.includes(allowed.trim()))

      if (!isValidOrigin && !isValidReferer) {
        console.warn('无效的请求来源:', { origin, referer })
        return NextResponse.json(
          {
            error: '请求来源无效',
            code: 'INVALID_ORIGIN',
          },
          { status: 403 },
        )
      }
    }

    // 生成安全的地图配置
    const config: AMapConfig = {
      version: '2.0',
      plugins: ['AMap.Geolocation', 'AMap.Geocoder', 'AMap.Scale'],
      securityJsCode: securityCode,
      loaderUrl: 'https://webapi.amap.com/loader.js',
      apiKey: apiKey, // 返回API Key给客户端使用
      timestamp: Date.now(),
    }

    // 缓存配置（5分钟）
    await amapCache.setConfig(config, 300)

    // 设置响应头
    const response = NextResponse.json({
      success: true,
      data: config,
    })

    response.headers.set('Cache-Control', 'public, max-age=300, s-maxage=300')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Cache', 'MISS')

    return response
  } catch (error) {
    console.error('获取地图配置失败:', error)

    return NextResponse.json(
      {
        error: '获取地图配置失败',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 },
    )
  }
}

// 支持CORS预检请求
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin')
  const allowedOrigins = process.env.AMAP_ALLOWED_ORIGINS?.split(',') || []

  const response = new NextResponse(null, { status: 200 })

  // 在开发环境中允许所有来源
  if (process.env.NODE_ENV === 'development') {
    response.headers.set('Access-Control-Allow-Origin', origin || '*')
  } else if (origin && allowedOrigins.some((allowed) => origin.includes(allowed.trim()))) {
    response.headers.set('Access-Control-Allow-Origin', origin)
  }

  response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  response.headers.set('Access-Control-Max-Age', '86400')

  return response
}
